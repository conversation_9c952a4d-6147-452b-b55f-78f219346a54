"use client";

import { useState, useEffect, useMemo } from "react";
import Image from "next/image";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  EyeIcon,
  LanguageIcon,
} from "@heroicons/react/24/outline";
import { clsx } from "clsx";
import type { Card, CardFormData, Description, Prediction } from "../lib/types";

const CARD_SETS = ["ไพ่ชุดเทพประจำวันเกิด", "Birthday Deity Card Set"];

export default function AdminPortal() {
  const [cards, setCards] = useState<Card[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCardSet, setFilterCardSet] = useState("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingCard, setEditingCard] = useState<Card | null>(null);
  const [viewingCard, setViewingCard] = useState<Card | null>(null);
  const [language, setLanguage] = useState<"th" | "en">("en");
  const [formData, setFormData] = useState<CardFormData>({
    name_th: "",
    name_en: "",
    cardSet_th: "",
    cardSet_en: "",
    imageUrl: "",
    descriptions: [],
    predictions: [],
  });
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);

  // Fetch cards from API
  const fetchCards = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append("search", searchTerm);
      if (filterCardSet !== "all") params.append("cardSet", filterCardSet);

      const response = await fetch(`/api/cards?${params}`);
      const result = await response.json();

      if (result.success) {
        setCards(result.data);
      }
    } catch (error) {
      console.error("Error fetching cards:", error);
    } finally {
      setFetchLoading(false);
    }
  };

  useEffect(() => {
    fetchCards();
  }, [searchTerm, filterCardSet]);

  // Filter cards
  const filteredCards = useMemo(() => {
    return cards.filter((card) => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        card.name.th.toLowerCase().includes(searchLower) ||
        card.name.en.toLowerCase().includes(searchLower) ||
        card.cardSet.th.toLowerCase().includes(searchLower) ||
        card.cardSet.en.toLowerCase().includes(searchLower);

      const matchesCardSet =
        filterCardSet === "all" ||
        card.cardSet.th === filterCardSet ||
        card.cardSet.en === filterCardSet;

      return matchesSearch && matchesCardSet;
    });
  }, [cards, searchTerm, filterCardSet]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      name_th: "",
      name_en: "",
      cardSet_th: "",
      cardSet_en: "",
      imageUrl: "",
      descriptions: [],
      predictions: [],
    });
    setEditingCard(null);
  };

  const openModal = (card?: Card) => {
    if (card) {
      setEditingCard(card);
      setFormData({
        name_th: card.name.th,
        name_en: card.name.en,
        cardSet_th: card.cardSet.th,
        cardSet_en: card.cardSet.en,
        imageUrl: card.imageUrl,
        descriptions: card.description || [],
        predictions: card.prediction || [],
      });
    } else {
      resetForm();
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  const openViewModal = (card: Card) => {
    setViewingCard(card);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setViewingCard(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const url = editingCard ? `/api/cards/${editingCard._id}` : "/api/cards";
      const method = editingCard ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        await fetchCards();
        closeModal();
      } else {
        alert("Error saving card: " + result.error);
      }
    } catch (error) {
      console.error("Error saving card:", error);
      alert("Error saving card");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (cardId: string) => {
    if (window.confirm("Are you sure you want to delete this card?")) {
      try {
        const response = await fetch(`/api/cards/${cardId}`, {
          method: "DELETE",
        });

        const result = await response.json();

        if (result.success) {
          await fetchCards();
        } else {
          alert("Error deleting card: " + result.error);
        }
      } catch (error) {
        console.error("Error deleting card:", error);
        alert("Error deleting card");
      }
    }
  };

  const addDescription = () => {
    setFormData((prev) => ({
      ...prev,
      descriptions: [
        ...prev.descriptions,
        {
          category: { th: "", en: "" },
          content: { th: "", en: "" },
          colorCode: "#000000",
        },
      ],
    }));
  };

  const updateDescription = (index: number, field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      descriptions: prev.descriptions.map((desc, i) => {
        if (i === index) {
          if (field.includes(".")) {
            const [parent, child] = field.split(".");
            return {
              ...desc,
              [parent]: {
                ...desc[parent as keyof Description],
                [child]: value,
              },
            };
          }
          return { ...desc, [field]: value };
        }
        return desc;
      }),
    }));
  };

  const removeDescription = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      descriptions: prev.descriptions.filter((_, i) => i !== index),
    }));
  };

  const addPrediction = () => {
    setFormData((prev) => ({
      ...prev,
      predictions: [
        ...prev.predictions,
        {
          category: { th: "", en: "" },
          content: { th: "", en: "" },
          colorCode: "#000000",
        },
      ],
    }));
  };

  const updatePrediction = (index: number, field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      predictions: prev.predictions.map((pred, i) => {
        if (i === index) {
          if (field.includes(".")) {
            const [parent, child] = field.split(".");
            return {
              ...pred,
              [parent]: {
                ...pred[parent as keyof Prediction],
                [child]: value,
              },
            };
          }
          return { ...pred, [field]: value };
        }
        return pred;
      }),
    }));
  };

  const removePrediction = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      predictions: prev.predictions.filter((_, i) => i !== index),
    }));
  };

  if (fetchLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cards...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Birthday Deity Cards Admin
              </h1>
              <p className="text-gray-600 mt-1">
                Manage your birthday deity cards collection
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setLanguage(language === "en" ? "th" : "en")}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
              >
                <LanguageIcon className="h-4 w-4" />
                {language === "en" ? "ภาษาไทย" : "English"}
              </button>
              <button
                onClick={() => openModal()}
                className="btn-primary flex items-center gap-2"
              >
                <PlusIcon className="h-5 w-5" />
                Add New Card
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="card mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search cards by name or set..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>

            {/* Card Set Filter */}
            <div className="flex gap-4">
              <select
                value={filterCardSet}
                onChange={(e) => setFilterCardSet(e.target.value)}
                className="input-field min-w-[200px]"
              >
                <option value="all">All Card Sets</option>
                {CARD_SETS.map((set) => (
                  <option key={set} value={set}>
                    {set}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredCards.length} of {cards.length} cards
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCards.map((card) => (
            <div
              key={card._id}
              className="card hover:shadow-md transition-shadow duration-200"
            >
              {/* Card Image */}
              <div className="w-full h-48 bg-gray-200 rounded-lg mb-4 relative overflow-hidden">
                {card.imageUrl ? (
                  <Image
                    src={card.imageUrl}
                    alt={card.name[language]}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = "none";
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No Image
                  </div>
                )}
              </div>

              {/* Card Details */}
              <div className="space-y-2">
                <div className="flex justify-between items-start">
                  <h3 className="text-xl font-semibold text-gray-900 line-clamp-2">
                    {card.name[language]}
                  </h3>
                </div>

                <div className="flex flex-wrap gap-2 text-sm">
                  <span className="tag tag-blue">{card.cardSet[language]}</span>
                  {card.description?.length > 0 && (
                    <span className="tag tag-purple">
                      {card.description.length} descriptions
                    </span>
                  )}
                  {card.prediction?.length > 0 && (
                    <span className="tag tag-green">
                      {card.prediction.length} predictions
                    </span>
                  )}
                </div>

                {/* First description preview */}
                {card.description?.[0] && (
                  <p className="text-gray-700 text-sm line-clamp-2">
                    {card.description[0].content[language].substring(0, 100)}...
                  </p>
                )}

                {/* Actions */}
                <div className="flex justify-end gap-2 pt-4">
                  <button
                    onClick={() => openViewModal(card)}
                    className="p-2 text-gray-600 hover:text-blue-600 transition-colors duration-200"
                    title="View card details"
                  >
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => openModal(card)}
                    className="p-2 text-gray-600 hover:text-primary-600 transition-colors duration-200"
                    title="Edit card"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDelete(card._id)}
                    className="p-2 text-gray-600 hover:text-red-600 transition-colors duration-200"
                    title="Delete card"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredCards.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <MagnifyingGlassIcon className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No cards found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || filterCardSet !== "all"
                ? "Try adjusting your search or filters"
                : "Get started by adding your first card"}
            </p>
            <button onClick={() => openModal()} className="btn-primary">
              Add New Card
            </button>
          </div>
        )}
      </main>

      {/* View Modal */}
      {isViewModalOpen && viewingCard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold text-gray-900">
                {viewingCard.name[language]}
              </h2>
              <button
                onClick={closeViewModal}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  {viewingCard.imageUrl && (
                    <div className="w-full h-64 relative rounded-lg overflow-hidden mb-4">
                      <Image
                        src={viewingCard.imageUrl}
                        alt={viewingCard.name[language]}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                </div>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900">Card Set</h3>
                    <p className="text-gray-700">
                      {viewingCard.cardSet[language]}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Names</h3>
                    <p className="text-gray-700">Thai: {viewingCard.name.th}</p>
                    <p className="text-gray-700">
                      English: {viewingCard.name.en}
                    </p>
                  </div>
                </div>
              </div>

              {/* Descriptions */}
              {viewingCard.description &&
                viewingCard.description.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Descriptions
                    </h3>
                    <div className="space-y-4">
                      {viewingCard.description.map((desc, index) => (
                        <div
                          key={index}
                          className="p-4 rounded-lg border-l-4"
                          style={{ borderLeftColor: desc.colorCode }}
                        >
                          <h4 className="font-medium text-gray-900 mb-2">
                            {desc.category[language]}
                          </h4>
                          <p className="text-gray-700 whitespace-pre-wrap">
                            {desc.content[language]}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              {/* Predictions */}
              {viewingCard.prediction && viewingCard.prediction.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Predictions
                  </h3>
                  <div className="space-y-4">
                    {viewingCard.prediction.map((pred, index) => (
                      <div
                        key={index}
                        className="p-4 rounded-lg border-l-4"
                        style={{ borderLeftColor: pred.colorCode }}
                      >
                        <h4 className="font-medium text-gray-900 mb-2">
                          {pred.category[language]}
                        </h4>
                        <p className="text-gray-700 whitespace-pre-wrap">
                          {pred.content[language]}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Edit/Add Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold text-gray-900">
                {editingCard ? "Edit Card" : "Add New Card"}
              </h2>
              <button
                onClick={closeModal}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Basic Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Name (Thai) *
                    </label>
                    <input
                      type="text"
                      name="name_th"
                      value={formData.name_th}
                      onChange={handleInputChange}
                      required
                      className="input-field"
                      placeholder="ชื่อไพ่ภาษาไทย"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Name (English) *
                    </label>
                    <input
                      type="text"
                      name="name_en"
                      value={formData.name_en}
                      onChange={handleInputChange}
                      required
                      className="input-field"
                      placeholder="Card name in English"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Card Set (Thai) *
                    </label>
                    <input
                      type="text"
                      name="cardSet_th"
                      value={formData.cardSet_th}
                      onChange={handleInputChange}
                      required
                      className="input-field"
                      placeholder="ชุดไพ่ภาษาไทย"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Card Set (English) *
                    </label>
                    <input
                      type="text"
                      name="cardSet_en"
                      value={formData.cardSet_en}
                      onChange={handleInputChange}
                      required
                      className="input-field"
                      placeholder="Card set in English"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image URL *
                  </label>
                  <input
                    type="url"
                    name="imageUrl"
                    value={formData.imageUrl}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </div>

              {/* Descriptions */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Descriptions
                  </h3>
                  <button
                    type="button"
                    onClick={addDescription}
                    className="btn-secondary text-sm"
                  >
                    Add Description
                  </button>
                </div>

                {formData.descriptions.map((desc, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-3">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-gray-900">
                        Description {index + 1}
                      </h4>
                      <button
                        type="button"
                        onClick={() => removeDescription(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category (Thai)
                        </label>
                        <input
                          type="text"
                          value={desc.category.th}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "category.th",
                              e.target.value
                            )
                          }
                          className="input-field"
                          placeholder="หมวดหมู่ภาษาไทย"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category (English)
                        </label>
                        <input
                          type="text"
                          value={desc.category.en}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "category.en",
                              e.target.value
                            )
                          }
                          className="input-field"
                          placeholder="Category in English"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Content (Thai)
                        </label>
                        <textarea
                          value={desc.content.th}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "content.th",
                              e.target.value
                            )
                          }
                          rows={4}
                          className="input-field resize-none"
                          placeholder="เนื้อหาภาษาไทย"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Content (English)
                        </label>
                        <textarea
                          value={desc.content.en}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "content.en",
                              e.target.value
                            )
                          }
                          rows={4}
                          className="input-field resize-none"
                          placeholder="Content in English"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Color Code
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={desc.colorCode}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "colorCode",
                              e.target.value
                            )
                          }
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={desc.colorCode}
                          onChange={(e) =>
                            updateDescription(
                              index,
                              "colorCode",
                              e.target.value
                            )
                          }
                          className="input-field flex-1"
                          placeholder="#000000"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Predictions */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Predictions
                  </h3>
                  <button
                    type="button"
                    onClick={addPrediction}
                    className="btn-secondary text-sm"
                  >
                    Add Prediction
                  </button>
                </div>

                {formData.predictions.map((pred, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-3">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-gray-900">
                        Prediction {index + 1}
                      </h4>
                      <button
                        type="button"
                        onClick={() => removePrediction(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category (Thai)
                        </label>
                        <input
                          type="text"
                          value={pred.category.th}
                          onChange={(e) =>
                            updatePrediction(
                              index,
                              "category.th",
                              e.target.value
                            )
                          }
                          className="input-field"
                          placeholder="หมวดหมู่ภาษาไทย"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category (English)
                        </label>
                        <input
                          type="text"
                          value={pred.category.en}
                          onChange={(e) =>
                            updatePrediction(
                              index,
                              "category.en",
                              e.target.value
                            )
                          }
                          className="input-field"
                          placeholder="Category in English"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Content (Thai)
                        </label>
                        <textarea
                          value={pred.content.th}
                          onChange={(e) =>
                            updatePrediction(
                              index,
                              "content.th",
                              e.target.value
                            )
                          }
                          rows={4}
                          className="input-field resize-none"
                          placeholder="เนื้อหาภาษาไทย"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Content (English)
                        </label>
                        <textarea
                          value={pred.content.en}
                          onChange={(e) =>
                            updatePrediction(
                              index,
                              "content.en",
                              e.target.value
                            )
                          }
                          rows={4}
                          className="input-field resize-none"
                          placeholder="Content in English"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Color Code
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={pred.colorCode}
                          onChange={(e) =>
                            updatePrediction(index, "colorCode", e.target.value)
                          }
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={pred.colorCode}
                          onChange={(e) =>
                            updatePrediction(index, "colorCode", e.target.value)
                          }
                          className="input-field flex-1"
                          placeholder="#000000"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={closeModal}
                  className="btn-secondary"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={clsx(
                    "btn-primary",
                    loading && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={loading}
                >
                  {loading
                    ? "Saving..."
                    : editingCard
                    ? "Update Card"
                    : "Add Card"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
